<?php

/**
 * Farmfaucet Database Updater - Consolidated
 *
 * This is the main consolidated database updater that runs all necessary updates
 * in the correct order to ensure the plugin database schema is complete and correct.
 */
class Farmfaucet_DB_Updater_Consolidated
{
    /**
     * Run all database updates in the correct order
     *
     * @return void
     */
    public static function run_updates()
    {
        try {
            // Step 1: Create base tables if they don't exist
            self::create_base_tables();
            
            // Step 2: Run the main database updater
            if (class_exists('Farmfaucet_DB_Updater')) {
                Farmfaucet_DB_Updater::run_updates(true);
            }
            
            // Step 3: Run security schema updates
            if (class_exists('Farmfaucet_Security')) {
                Farmfaucet_Security::update_database_schema();
            }
            
            // Step 4: Ensure all critical tables exist
            self::ensure_critical_tables();
            
            // Step 5: Update version tracking
            self::update_version_tracking();
            
        } catch (Exception $e) {
            error_log('Farmfaucet_DB_Updater_Consolidated error: ' . $e->getMessage());
        }
    }
    
    /**
     * Create base tables if they don't exist
     *
     * @return void
     */
    private static function create_base_tables()
    {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();
        
        // Create faucets table with complete structure
        $faucets_table = $wpdb->prefix . 'farmfaucet_faucets';
        $faucets_exists = $wpdb->get_var("SHOW TABLES LIKE '{$faucets_table}'") === $faucets_table;
        
        if (!$faucets_exists) {
            $sql = "CREATE TABLE {$faucets_table} (
                id mediumint(9) NOT NULL AUTO_INCREMENT,
                name varchar(255) NOT NULL,
                faucet_type varchar(20) NOT NULL DEFAULT 'stage',
                currency varchar(20) NOT NULL,
                amount varchar(50) NOT NULL,
                cooldown int(11) NOT NULL,
                shortcode varchar(50) NOT NULL,
                api_key varchar(255) DEFAULT '' NOT NULL,
                captcha_type varchar(20) DEFAULT '' NOT NULL,
                faucet_color varchar(50) DEFAULT 'green' NOT NULL,
                is_enabled tinyint(1) NOT NULL DEFAULT 1,
                transparent_bg tinyint(1) NOT NULL DEFAULT 0,
                bg_style varchar(20) NOT NULL DEFAULT 'solid',
                bg_color varchar(50) NOT NULL DEFAULT '#f8fff8',
                bg_gradient_start varchar(50) NOT NULL DEFAULT '#f8fff8',
                bg_gradient_end varchar(50) NOT NULL DEFAULT '#e8f5e9',
                text_color varchar(50) NOT NULL DEFAULT '#4CAF50',
                text_shadow varchar(50) NOT NULL DEFAULT 'none',
                button_color varchar(50) NOT NULL DEFAULT '#4CAF50',
                border_color varchar(50) NOT NULL DEFAULT '#4CAF50',
                border_radius varchar(20) NOT NULL DEFAULT '8px',
                button_border_radius varchar(20) NOT NULL DEFAULT '25px',
                input_label_color varchar(50) NOT NULL DEFAULT '#333333',
                input_placeholder_color varchar(50) NOT NULL DEFAULT '#999999',
                form_bg_color varchar(50) NOT NULL DEFAULT '#ffffff',
                form_transparent tinyint(1) NOT NULL DEFAULT 0,
                currency_id bigint(20) DEFAULT 0,
                min_withdrawal varchar(50) DEFAULT '0',
                available_currencies longtext DEFAULT NULL,
                conversion_currencies longtext DEFAULT NULL,
                view_style varchar(20) DEFAULT 'default',
                ads_only_conversion tinyint(1) DEFAULT 0,
                created_at datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
                updated_at datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
                PRIMARY KEY (id),
                UNIQUE KEY shortcode (shortcode),
                KEY currency (currency),
                KEY faucet_type (faucet_type),
                KEY is_enabled (is_enabled),
                KEY created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
            
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
        }
        
        // Create logs table
        $logs_table = $wpdb->prefix . 'farmfaucet_logs';
        $logs_exists = $wpdb->get_var("SHOW TABLES LIKE '{$logs_table}'") === $logs_table;
        
        if (!$logs_exists) {
            $sql = "CREATE TABLE {$logs_table} (
                id mediumint(9) NOT NULL AUTO_INCREMENT,
                timestamp datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
                message text NOT NULL,
                type varchar(20) NOT NULL,
                faucet_id int(11) DEFAULT 0 NOT NULL,
                PRIMARY KEY (id),
                KEY type (type),
                KEY faucet_id (faucet_id),
                KEY timestamp (timestamp)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
            
            dbDelta($sql);
        }
        
        // Create buttons table
        $buttons_table = $wpdb->prefix . 'farmfaucet_buttons';
        $buttons_exists = $wpdb->get_var("SHOW TABLES LIKE '{$buttons_table}'") === $buttons_table;
        
        if (!$buttons_exists) {
            $sql = "CREATE TABLE {$buttons_table} (
                id mediumint(9) NOT NULL AUTO_INCREMENT,
                faucet_id mediumint(9) NOT NULL,
                button_text varchar(255) NOT NULL,
                button_url varchar(500) NOT NULL,
                button_color varchar(50) NOT NULL DEFAULT 'blue',
                button_size varchar(20) NOT NULL DEFAULT 'medium',
                button_shape varchar(20) NOT NULL DEFAULT 'rounded',
                is_enabled tinyint(1) NOT NULL DEFAULT 1,
                display_order int(11) NOT NULL DEFAULT 0,
                created_at datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
                updated_at datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
                PRIMARY KEY (id),
                KEY faucet_id (faucet_id),
                KEY is_enabled (is_enabled),
                KEY display_order (display_order)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
            
            dbDelta($sql);
        }
    }
    
    /**
     * Ensure all critical tables exist
     *
     * @return void
     */
    private static function ensure_critical_tables()
    {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();
        
        $critical_tables = [
            'farmfaucet_users' => "CREATE TABLE %s (
                id mediumint(9) NOT NULL AUTO_INCREMENT,
                user_hash varchar(64) NOT NULL,
                display_name varchar(50) DEFAULT NULL,
                profile_picture varchar(255) DEFAULT NULL,
                created_at datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
                updated_at datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
                PRIMARY KEY (id),
                UNIQUE KEY user_hash (user_hash),
                KEY created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;",
            
            'farmfaucet_leaderboard' => "CREATE TABLE %s (
                id mediumint(9) NOT NULL AUTO_INCREMENT,
                user_hash varchar(64) NOT NULL,
                total_completions int(11) NOT NULL DEFAULT 0,
                last_completion datetime DEFAULT NULL,
                created_at datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
                updated_at datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
                PRIMARY KEY (id),
                UNIQUE KEY user_hash (user_hash),
                KEY total_completions (total_completions),
                KEY last_completion (last_completion)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;",
            
            'farmfaucet_claims' => "CREATE TABLE %s (
                id mediumint(9) NOT NULL AUTO_INCREMENT,
                user_hash varchar(64) NOT NULL,
                faucet_id mediumint(9) NOT NULL,
                timestamp datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
                amount varchar(50) NOT NULL,
                currency varchar(20) NOT NULL,
                status varchar(20) NOT NULL DEFAULT 'success',
                PRIMARY KEY (id),
                KEY user_hash (user_hash),
                KEY faucet_id (faucet_id),
                KEY timestamp (timestamp),
                KEY status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;"
        ];
        
        foreach ($critical_tables as $table_name => $sql_template) {
            $full_table_name = $wpdb->prefix . $table_name;
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$full_table_name}'") === $full_table_name;
            
            if (!$table_exists) {
                $sql = sprintf($sql_template, $full_table_name);
                require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
                dbDelta($sql);
            }
        }
    }
    
    /**
     * Update version tracking
     *
     * @return void
     */
    private static function update_version_tracking()
    {
        // Update the database version
        update_option('farmfaucet_db_version', FARMFAUCET_VERSION);
        update_option('farmfaucet_last_db_update', current_time('mysql'));
    }
}
