<?php

/**
 * Farm Faucet Database Schema Fix
 * 
 * This script fixes all database schema issues by adding missing columns
 * and ensuring proper data types for the Farm Faucet plugin.
 */

// Load WordPress
require_once(dirname(__FILE__) . '/../../../wp-load.php');

// Security check
if (!current_user_can('manage_options')) {
    wp_die('You do not have permission to run this script.');
}

echo '<!DOCTYPE html>
<html>
<head>
    <title>Farm Faucet Database Schema Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { background: #d4edda; color: #155724; padding: 10px; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 10px; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 10px; border: 1px solid #ffeaa7; border-radius: 4px; margin: 10px 0; }
        h1, h2 { color: #333; }
        .step { margin: 20px 0; }
    </style>
</head>
<body>';

echo '<h1>🔧 Farm Faucet Database Schema Fix</h1>';

global $wpdb;
$faucets_table = $wpdb->prefix . 'farmfaucet_faucets';
$currencies_table = $wpdb->prefix . 'farmfaucet_currencies';
$bots_table = $wpdb->prefix . 'farmfaucet_tg_bots';
$buttons_table = $wpdb->prefix . 'farmfaucet_buttons';

$fixes_applied = [];
$errors = [];

// Step 1: Check if faucets table exists
echo '<div class="step"><h2>Step 1: Checking Faucets Table</h2>';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$faucets_table}'") === $faucets_table;

if (!$table_exists) {
    echo '<div class="error">❌ Faucets table does not exist! Creating it now...</div>';

    // Create the complete faucets table with all required columns
    $charset_collate = $wpdb->get_charset_collate();
    $sql = "CREATE TABLE {$faucets_table} (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        name varchar(255) NOT NULL,
        faucet_type varchar(20) NOT NULL DEFAULT 'stage',
        currency varchar(20) NOT NULL,
        amount varchar(50) NOT NULL,
        cooldown int(11) NOT NULL,
        shortcode varchar(50) NOT NULL,
        api_key varchar(255) DEFAULT '' NOT NULL,
        captcha_type varchar(20) DEFAULT '' NOT NULL,
        faucet_color varchar(20) DEFAULT 'green' NOT NULL,
        is_enabled tinyint(1) NOT NULL DEFAULT 1,
        transparent_bg tinyint(1) NOT NULL DEFAULT 0,
        bg_style varchar(20) NOT NULL DEFAULT 'solid',
        bg_color varchar(50) NOT NULL DEFAULT '#f8fff8',
        bg_gradient_start varchar(50) NOT NULL DEFAULT '#f8fff8',
        bg_gradient_end varchar(50) NOT NULL DEFAULT '#e8f5e9',
        text_color varchar(50) NOT NULL DEFAULT '#4CAF50',
        text_shadow varchar(50) NOT NULL DEFAULT 'none',
        button_color varchar(50) NOT NULL DEFAULT '#4CAF50',
        border_color varchar(50) NOT NULL DEFAULT '#4CAF50',
        border_radius varchar(20) NOT NULL DEFAULT '8px',
        button_border_radius varchar(20) NOT NULL DEFAULT '25px',
        input_label_color varchar(50) NOT NULL DEFAULT '#333333',
        input_placeholder_color varchar(50) NOT NULL DEFAULT '#999999',
        form_bg_color varchar(50) NOT NULL DEFAULT '#ffffff',
        form_transparent tinyint(1) NOT NULL DEFAULT 0,
        currency_id bigint(20) DEFAULT 0,
        min_withdrawal varchar(50) DEFAULT '0',
        available_currencies longtext DEFAULT NULL,
        conversion_currencies longtext DEFAULT NULL,
        view_style varchar(20) DEFAULT 'default',
        ads_only_conversion tinyint(1) DEFAULT 0,
        created_at datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
        updated_at datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
        PRIMARY KEY (id),
        UNIQUE KEY shortcode (shortcode),
        KEY currency (currency),
        KEY faucet_type (faucet_type),
        KEY is_enabled (is_enabled),
        KEY created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    $result = dbDelta($sql);

    if ($wpdb->get_var("SHOW TABLES LIKE '{$faucets_table}'") === $faucets_table) {
        echo '<div class="success">✅ Successfully created faucets table with all required columns</div>';
        $fixes_applied[] = 'Created complete faucets table';
    } else {
        echo '<div class="error">❌ Failed to create faucets table</div>';
        $errors[] = 'Failed to create faucets table';
    }
} else {
    echo '<div class="info">✅ Faucets table exists</div>';

    // Get existing columns
    $columns = $wpdb->get_results("SHOW COLUMNS FROM {$faucets_table}");
    $column_names = array_map(function ($col) {
        return $col->Field;
    }, $columns);

    echo '<div class="info">Current columns: ' . implode(', ', $column_names) . '</div>';

    // Define all required columns with their definitions
    $required_columns = [
        'faucet_type' => "varchar(20) NOT NULL DEFAULT 'stage'",
        'faucet_color' => "varchar(20) DEFAULT 'green' NOT NULL",
        'is_enabled' => "tinyint(1) NOT NULL DEFAULT 1",
        'transparent_bg' => "tinyint(1) NOT NULL DEFAULT 0",
        'bg_style' => "varchar(20) NOT NULL DEFAULT 'solid'",
        'bg_color' => "varchar(50) NOT NULL DEFAULT '#f8fff8'",
        'bg_gradient_start' => "varchar(50) NOT NULL DEFAULT '#f8fff8'",
        'bg_gradient_end' => "varchar(50) NOT NULL DEFAULT '#e8f5e9'",
        'text_color' => "varchar(50) NOT NULL DEFAULT '#4CAF50'",
        'text_shadow' => "varchar(50) NOT NULL DEFAULT 'none'",
        'button_color' => "varchar(50) NOT NULL DEFAULT '#4CAF50'",
        'border_color' => "varchar(50) NOT NULL DEFAULT '#4CAF50'",
        'border_radius' => "varchar(20) NOT NULL DEFAULT '8px'",
        'button_border_radius' => "varchar(20) NOT NULL DEFAULT '25px'",
        'input_label_color' => "varchar(50) NOT NULL DEFAULT '#333333'",
        'input_placeholder_color' => "varchar(50) NOT NULL DEFAULT '#999999'",
        'form_bg_color' => "varchar(50) NOT NULL DEFAULT '#ffffff'",
        'form_transparent' => "tinyint(1) NOT NULL DEFAULT 0",
        'currency_id' => "bigint(20) DEFAULT 0",
        'min_withdrawal' => "varchar(50) DEFAULT '0'",
        'available_currencies' => "longtext DEFAULT NULL",
        'conversion_currencies' => "longtext DEFAULT NULL",
        'view_style' => "varchar(20) DEFAULT 'default'",
        'ads_only_conversion' => "tinyint(1) DEFAULT 0"
    ];

    // Add missing columns
    $missing_columns = array_diff(array_keys($required_columns), $column_names);

    if (!empty($missing_columns)) {
        echo '<div class="warning">Missing columns found: ' . implode(', ', $missing_columns) . '</div>';

        foreach ($missing_columns as $column) {
            $definition = $required_columns[$column];
            $query = "ALTER TABLE {$faucets_table} ADD COLUMN {$column} {$definition}";
            $result = $wpdb->query($query);

            if ($result !== false) {
                echo '<div class="success">✅ Added column: ' . $column . '</div>';
                $fixes_applied[] = "Added column: {$column}";
            } else {
                echo '<div class="error">❌ Failed to add column: ' . $column . ' - ' . $wpdb->last_error . '</div>';
                $errors[] = "Failed to add column: {$column}";
            }
        }
    } else {
        echo '<div class="success">✅ All required columns exist in faucets table</div>';
    }
}
echo '</div>';

// Step 2: Check and create currencies table
echo '<div class="step"><h2>Step 2: Checking Currencies Table</h2>';
$currencies_exists = $wpdb->get_var("SHOW TABLES LIKE '{$currencies_table}'") === $currencies_table;

if (!$currencies_exists) {
    echo '<div class="warning">⚠️ Currencies table does not exist! Creating it now...</div>';

    $sql = "CREATE TABLE {$currencies_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        name varchar(100) NOT NULL,
        code varchar(10) NOT NULL,
        symbol varchar(10) NOT NULL,
        base_currency varchar(10) NOT NULL,
        exchange_rate decimal(18,8) NOT NULL,
        color varchar(20) DEFAULT '#4CAF50',
        icon varchar(255) DEFAULT NULL,
        currency_type varchar(20) DEFAULT 'earnings',
        is_active tinyint(1) NOT NULL DEFAULT 1,
        created_by bigint(20) NOT NULL,
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY code (code),
        KEY base_currency (base_currency),
        KEY currency_type (currency_type),
        KEY is_active (is_active)
    ) {$charset_collate};";

    $result = dbDelta($sql);

    if ($wpdb->get_var("SHOW TABLES LIKE '{$currencies_table}'") === $currencies_table) {
        echo '<div class="success">✅ Successfully created currencies table</div>';
        $fixes_applied[] = 'Created currencies table';
    } else {
        echo '<div class="error">❌ Failed to create currencies table</div>';
        $errors[] = 'Failed to create currencies table';
    }
} else {
    echo '<div class="success">✅ Currencies table exists</div>';
}
echo '</div>';

// Step 3: Check and create user currency balances table
echo '<div class="step"><h2>Step 3: Checking User Currency Balances Table</h2>';
$balances_table = $wpdb->prefix . 'farmfaucet_user_currency_balances';
$balances_exists = $wpdb->get_var("SHOW TABLES LIKE '{$balances_table}'") === $balances_table;

if (!$balances_exists) {
    echo '<div class="warning">⚠️ User currency balances table does not exist! Creating it now...</div>';

    $sql = "CREATE TABLE {$balances_table} (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        currency_id bigint(20) NOT NULL,
        balance decimal(18,8) NOT NULL DEFAULT 0,
        last_updated datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY user_currency (user_id, currency_id),
        KEY user_id (user_id),
        KEY currency_id (currency_id)
    ) {$charset_collate};";

    $result = dbDelta($sql);

    if ($wpdb->get_var("SHOW TABLES LIKE '{$balances_table}'") === $balances_table) {
        echo '<div class="success">✅ Successfully created user currency balances table</div>';
        $fixes_applied[] = 'Created user currency balances table';
    } else {
        echo '<div class="error">❌ Failed to create user currency balances table</div>';
        $errors[] = 'Failed to create user currency balances table';
    }
} else {
    echo '<div class="success">✅ User currency balances table exists</div>';
}
echo '</div>';

// Step 4: Check and create Telegram bots table
echo '<div class="step"><h2>Step 4: Checking Telegram Bots Table</h2>';
$bots_exists = $wpdb->get_var("SHOW TABLES LIKE '{$bots_table}'") === $bots_table;

if (!$bots_exists) {
    echo '<div class="warning">⚠️ Telegram bots table does not exist! Creating it now...</div>';

    $sql = "CREATE TABLE {$bots_table} (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        bot_name varchar(100) NOT NULL,
        bot_token varchar(255) NOT NULL,
        bot_username varchar(100) NOT NULL,
        bot_type varchar(20) NOT NULL DEFAULT 'text',
        webhook_url varchar(255) DEFAULT '',
        created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
        settings longtext DEFAULT NULL,
        is_active tinyint(1) DEFAULT 1,
        PRIMARY KEY (id),
        UNIQUE KEY bot_token (bot_token)
    ) {$charset_collate};";

    $result = dbDelta($sql);

    if ($wpdb->get_var("SHOW TABLES LIKE '{$bots_table}'") === $bots_table) {
        echo '<div class="success">✅ Successfully created Telegram bots table</div>';
        $fixes_applied[] = 'Created Telegram bots table';
    } else {
        echo '<div class="error">❌ Failed to create Telegram bots table</div>';
        $errors[] = 'Failed to create Telegram bots table';
    }
} else {
    echo '<div class="success">✅ Telegram bots table exists</div>';
}
echo '</div>';

// Step 5: Summary
echo '<div class="step"><h2>🎯 Summary</h2>';

if (!empty($fixes_applied)) {
    echo '<div class="success"><h3>✅ Fixes Applied:</h3><ul>';
    foreach ($fixes_applied as $fix) {
        echo '<li>' . esc_html($fix) . '</li>';
    }
    echo '</ul></div>';
}

if (!empty($errors)) {
    echo '<div class="error"><h3>❌ Errors Encountered:</h3><ul>';
    foreach ($errors as $error) {
        echo '<li>' . esc_html($error) . '</li>';
    }
    echo '</ul></div>';
}

if (empty($errors)) {
    echo '<div class="success"><h2>🎉 Database Schema Fix Complete!</h2>';
    echo '<p>All required database tables and columns have been created successfully.</p>';
    echo '<p><strong>Next Steps:</strong></p>';
    echo '<ol>';
    echo '<li>Re-enable the disabled components in farmfaucet.php</li>';
    echo '<li>Test faucet creation and settings saving</li>';
    echo '<li>Verify currency creation functionality</li>';
    echo '</ol>';
} else {
    echo '<div class="warning"><h2>⚠️ Partial Success</h2>';
    echo '<p>Some issues were fixed, but errors remain. Please check the errors above and resolve them manually.</p>';
}

echo '<p><a href="' . admin_url('admin.php?page=farmfaucet') . '" class="button button-primary">Return to Farm Faucet Admin</a></p>';
echo '</div>';

echo '</body></html>';
