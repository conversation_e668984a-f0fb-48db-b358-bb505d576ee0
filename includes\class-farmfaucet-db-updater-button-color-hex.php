<?php

/**
 * Farmfaucet Database Updater - Button Color Hex
 *
 * Updates button color fields to support hex color values
 */
class Farmfaucet_DB_Updater_Button_Color_Hex
{
    /**
     * Run the button color hex update
     *
     * @return void
     */
    public static function run_update()
    {
        global $wpdb;
        $buttons_table = $wpdb->prefix . 'farmfaucet_buttons';
        $faucets_table = $wpdb->prefix . 'farmfaucet_faucets';

        // Check if buttons table exists
        $buttons_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$buttons_table}'") === $buttons_table;
        
        if ($buttons_table_exists) {
            // Get existing columns
            $columns = $wpdb->get_results("SHOW COLUMNS FROM {$buttons_table}");
            $column_names = array_map(function ($col) {
                return $col->Field;
            }, $columns);

            // Update button_color column to support hex values
            if (in_array('button_color', $column_names)) {
                $wpdb->query("ALTER TABLE {$buttons_table} MODIFY COLUMN button_color varchar(50) NOT NULL DEFAULT 'blue'");
            }

            // Update milestone color columns to support hex values
            $milestone_color_columns = [
                'milestone_card_bg_color' => "varchar(50) NOT NULL DEFAULT '#FFFFFF'",
                'milestone_card_gradient_start' => "varchar(50) NOT NULL DEFAULT '#FFFFFF'",
                'milestone_card_gradient_end' => "varchar(50) NOT NULL DEFAULT '#F5F5F5'",
                'milestone_bar_color' => "varchar(50) NOT NULL DEFAULT '#4CAF50'",
                'milestone_gradient_start' => "varchar(50) NOT NULL DEFAULT '#4CAF50'",
                'milestone_gradient_end' => "varchar(50) NOT NULL DEFAULT '#2196F3'"
            ];

            foreach ($milestone_color_columns as $column => $definition) {
                if (in_array($column, $column_names)) {
                    $wpdb->query("ALTER TABLE {$buttons_table} MODIFY COLUMN {$column} {$definition}");
                }
            }
        }

        // Check if faucets table exists and update color columns
        $faucets_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$faucets_table}'") === $faucets_table;
        
        if ($faucets_table_exists) {
            // Get existing columns
            $columns = $wpdb->get_results("SHOW COLUMNS FROM {$faucets_table}");
            $column_names = array_map(function ($col) {
                return $col->Field;
            }, $columns);

            // Update faucet color columns to support hex values
            $faucet_color_columns = [
                'faucet_color' => "varchar(50) DEFAULT 'green' NOT NULL",
                'bg_color' => "varchar(50) NOT NULL DEFAULT '#f8fff8'",
                'bg_gradient_start' => "varchar(50) NOT NULL DEFAULT '#f8fff8'",
                'bg_gradient_end' => "varchar(50) NOT NULL DEFAULT '#e8f5e9'",
                'text_color' => "varchar(50) NOT NULL DEFAULT '#4CAF50'",
                'button_color' => "varchar(50) NOT NULL DEFAULT '#4CAF50'",
                'border_color' => "varchar(50) NOT NULL DEFAULT '#4CAF50'",
                'input_label_color' => "varchar(50) NOT NULL DEFAULT '#333333'",
                'input_placeholder_color' => "varchar(50) NOT NULL DEFAULT '#999999'",
                'form_bg_color' => "varchar(50) NOT NULL DEFAULT '#ffffff'"
            ];

            foreach ($faucet_color_columns as $column => $definition) {
                if (in_array($column, $column_names)) {
                    $wpdb->query("ALTER TABLE {$faucets_table} MODIFY COLUMN {$column} {$definition}");
                }
            }
        }
    }
}
