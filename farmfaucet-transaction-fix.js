/**
 * Farm Faucet Transaction Display Fix
 * JavaScript to fix transaction tab display and filtering
 */

jQuery(document).ready(function($) {
    console.log('Farm Faucet Transaction Fix loaded');
    
    // Fix 1: Ensure transaction tab content is visible
    function showTransactionContent() {
        $('.transaction-log-section').show();
        $('.transaction-tab-content').show();
        
        // Initialize jQuery UI tabs if available
        if ($.fn.tabs) {
            $('#transaction-tabs').tabs();
        }
        
        console.log('Transaction content made visible');
    }
    
    // Fix 2: Handle main tab switching
    $('.nav-tab').on('click', function(e) {
        var href = $(this).attr('href');
        if (href && href.includes('tab=faucets')) {
            console.log('Faucets tab clicked - showing transaction content');
            setTimeout(showTransactionContent, 100);
        }
    });
    
    // Fix 3: Handle transaction subtab filtering
    $(document).on('click', '.transaction-subtab', function(e) {
        e.preventDefault();
        
        var $this = $(this);
        var type = $this.data('type');
        var faucetId = $this.data('faucet');
        var container = $this.closest('.transaction-container').find('.transaction-chat-box');
        
        console.log('Transaction filter clicked:', type, faucetId);
        
        // Hide all messages first
        container.find('.transaction-message').hide();
        
        // Show filtered messages
        if (type === 'all') {
            container.find('.transaction-message').show();
        } else {
            container.find('.transaction-message[data-type="' + type + '"]').show();
        }
        
        // Update active tab styling
        $this.siblings('.transaction-subtab').removeClass('active error-active');
        $this.addClass('active');
        
        // Special styling for error tab
        if (type === 'error') {
            $this.addClass('error-active');
        }
        
        // Auto-scroll to bottom
        container.scrollTop(container[0].scrollHeight);
        
        console.log('Transaction filtering applied for type:', type);
    });
    
    // Fix 4: Initialize default state
    function initializeTransactionTabs() {
        $('.transaction-chat-box').each(function() {
            var $chatBox = $(this);
            var $container = $chatBox.closest('.transaction-container');
            var $firstTab = $container.find('.transaction-subtab').first();
            
            // Set first tab as active
            $firstTab.addClass('active');
            
            // Show all messages by default
            $chatBox.find('.transaction-message').show();
            
            // Auto-scroll to bottom
            $chatBox.scrollTop($chatBox[0].scrollHeight);
        });
        
        console.log('Transaction tabs initialized');
    }
    
    // Fix 5: Handle page load
    $(window).on('load', function() {
        setTimeout(function() {
            showTransactionContent();
            initializeTransactionTabs();
        }, 500);
    });
    
    // Fix 6: Handle AJAX content loading
    $(document).ajaxComplete(function() {
        setTimeout(function() {
            showTransactionContent();
            initializeTransactionTabs();
        }, 100);
    });
    
    // Fix 7: Force show transaction content if hidden
    setInterval(function() {
        if ($('.nav-tab-active').attr('href') && $('.nav-tab-active').attr('href').includes('tab=faucets')) {
            if ($('.transaction-log-section').is(':hidden')) {
                console.log('Transaction content was hidden - forcing show');
                showTransactionContent();
            }
        }
    }, 2000);
    
    // Fix 8: Add sample transactions for testing (admin only)
    if (window.location.href.includes('wp-admin')) {
        var $addSampleBtn = $('<button type="button" class="button" style="margin: 10px 0;">Add Sample Transactions</button>');
        $addSampleBtn.on('click', function() {
            var sampleTransactions = [
                {
                    type: 'success',
                    message: 'Successfully claimed 0.00001 BTC from Test Faucet',
                    timestamp: new Date().toLocaleString()
                },
                {
                    type: 'error', 
                    message: 'Failed to process claim: Insufficient balance (error_code: 1001)',
                    timestamp: new Date().toLocaleString()
                }
            ];
            
            sampleTransactions.forEach(function(txn) {
                var $message = $('<div class="transaction-message ' + txn.type + '" data-type="' + txn.type + '">' +
                    '<div class="transaction-message-header">' +
                    '<div class="txn-content">' + txn.message +
                    '<div class="txn-meta">' +
                    '<span class="txn-timestamp">' + txn.timestamp + '</span>' +
                    '<span class="txn-status">' + (txn.type === 'error' ? '❌' : '💰') + '</span>' +
                    '</div></div></div></div>');
                
                $('.transaction-chat-box').append($message);
            });
            
            // Scroll to bottom
            $('.transaction-chat-box').scrollTop($('.transaction-chat-box')[0].scrollHeight);
            
            alert('Sample transactions added for testing');
        });
        
        $('.transaction-log-section').prepend($addSampleBtn);
    }
    
    console.log('Farm Faucet Transaction Fix initialization complete');
});
