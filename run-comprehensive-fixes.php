<?php
/**
 * Farm Faucet Comprehensive Fix Runner
 * 
 * This script runs all fixes in the correct order and validates the results
 */

// Load WordPress
$wp_load_path = dirname(__FILE__) . '/../../../wp-load.php';
if (file_exists($wp_load_path)) {
    require_once($wp_load_path);
} else {
    die('WordPress not found. Please run this script from the plugin directory.');
}

// Security check
if (!function_exists('current_user_can') || !current_user_can('manage_options')) {
    die('You do not have permission to run this script.');
}

echo '<!DOCTYPE html>
<html>
<head>
    <title>Farm Faucet Comprehensive Fix Runner</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { background: #d4edda; color: #155724; padding: 10px; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 10px; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 10px; border: 1px solid #ffeaa7; border-radius: 4px; margin: 10px 0; }
        h1, h2 { color: #333; }
        .step { margin: 20px 0; }
        .progress { background: #f0f0f0; border-radius: 10px; padding: 3px; margin: 10px 0; }
        .progress-bar { background: #4CAF50; height: 20px; border-radius: 7px; transition: width 0.3s; }
        .button { display: inline-block; padding: 10px 20px; background: #4CAF50; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .button:hover { background: #45a049; }
    </style>
</head>
<body>';

echo '<h1>🚀 Farm Faucet Comprehensive Fix Runner</h1>';

$total_steps = 6;
$current_step = 0;
$all_fixes_successful = true;
$fix_results = [];

function update_progress($step, $total) {
    $percentage = ($step / $total) * 100;
    echo '<div class="progress"><div class="progress-bar" style="width: ' . $percentage . '%"></div></div>';
    echo '<p>Progress: ' . $step . '/' . $total . ' (' . round($percentage) . '%)</p>';
}

// Step 1: Load and validate all required classes
echo '<div class="step"><h2>Step 1: Loading Required Classes</h2>';
$current_step++;
update_progress($current_step, $total_steps);

$required_classes = [
    'Farmfaucet_DB_Updater_Button_Color_Hex',
    'Farmfaucet_DB_Updater_Countdown_Captcha', 
    'Farmfaucet_DB_Updater_Form_Bg',
    'Farmfaucet_DB_Updater_Faucet_Appearance',
    'Farmfaucet_DB_Updater_Consolidated',
    'Farmfaucet_Admin',
    'Farmfaucet_Logger',
    'Farmfaucet_Security'
];

$missing_classes = [];
foreach ($required_classes as $class) {
    if (!class_exists($class)) {
        $missing_classes[] = $class;
    }
}

if (empty($missing_classes)) {
    echo '<div class="success">✅ All required classes loaded successfully</div>';
    $fix_results['class_loading'] = 'success';
} else {
    echo '<div class="error">❌ Missing classes: ' . implode(', ', $missing_classes) . '</div>';
    $fix_results['class_loading'] = 'failed';
    $all_fixes_successful = false;
}
echo '</div>';

// Step 2: Run database schema fixes
echo '<div class="step"><h2>Step 2: Running Database Schema Fixes</h2>';
$current_step++;
update_progress($current_step, $total_steps);

try {
    if (class_exists('Farmfaucet_DB_Updater_Consolidated')) {
        Farmfaucet_DB_Updater_Consolidated::run_updates();
        echo '<div class="success">✅ Consolidated database updates completed</div>';
        $fix_results['database_schema'] = 'success';
    } else {
        echo '<div class="error">❌ Farmfaucet_DB_Updater_Consolidated class not found</div>';
        $fix_results['database_schema'] = 'failed';
        $all_fixes_successful = false;
    }
} catch (Exception $e) {
    echo '<div class="error">❌ Database schema fix failed: ' . esc_html($e->getMessage()) . '</div>';
    $fix_results['database_schema'] = 'failed';
    $all_fixes_successful = false;
}
echo '</div>';

// Step 3: Run individual database updaters
echo '<div class="step"><h2>Step 3: Running Individual Database Updaters</h2>';
$current_step++;
update_progress($current_step, $total_steps);

$updaters = [
    'Farmfaucet_DB_Updater_Button_Color_Hex' => 'Button Color Hex',
    'Farmfaucet_DB_Updater_Countdown_Captcha' => 'Countdown Captcha',
    'Farmfaucet_DB_Updater_Form_Bg' => 'Form Background',
    'Farmfaucet_DB_Updater_Faucet_Appearance' => 'Faucet Appearance'
];

$updater_results = [];
foreach ($updaters as $class => $name) {
    try {
        if (class_exists($class)) {
            $class::run_update();
            echo '<div class="success">✅ ' . $name . ' updater completed</div>';
            $updater_results[$class] = 'success';
        } else {
            echo '<div class="error">❌ ' . $name . ' updater class not found</div>';
            $updater_results[$class] = 'failed';
            $all_fixes_successful = false;
        }
    } catch (Exception $e) {
        echo '<div class="error">❌ ' . $name . ' updater failed: ' . esc_html($e->getMessage()) . '</div>';
        $updater_results[$class] = 'failed';
        $all_fixes_successful = false;
    }
}

$fix_results['individual_updaters'] = $updater_results;
echo '</div>';

// Step 4: Validate database structure
echo '<div class="step"><h2>Step 4: Validating Database Structure</h2>';
$current_step++;
update_progress($current_step, $total_steps);

global $wpdb;
$faucets_table = $wpdb->prefix . 'farmfaucet_faucets';

// Check if table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$faucets_table}'") === $faucets_table;

if ($table_exists) {
    echo '<div class="success">✅ Faucets table exists</div>';
    
    // Check critical columns
    $columns = $wpdb->get_results("SHOW COLUMNS FROM {$faucets_table}");
    $column_names = array_map(function ($col) {
        return $col->Field;
    }, $columns);
    
    $critical_columns = [
        'faucet_type', 'faucet_color', 'is_enabled', 'transparent_bg',
        'bg_style', 'bg_color', 'text_color', 'button_color', 'border_color',
        'form_bg_color', 'form_transparent'
    ];
    
    $missing_columns = array_diff($critical_columns, $column_names);
    
    if (empty($missing_columns)) {
        echo '<div class="success">✅ All critical columns present</div>';
        $fix_results['database_validation'] = 'success';
    } else {
        echo '<div class="warning">⚠️ Missing columns: ' . implode(', ', $missing_columns) . '</div>';
        $fix_results['database_validation'] = 'partial';
    }
} else {
    echo '<div class="error">❌ Faucets table does not exist</div>';
    $fix_results['database_validation'] = 'failed';
    $all_fixes_successful = false;
}
echo '</div>';

// Step 5: Test faucet operations
echo '<div class="step"><h2>Step 5: Testing Faucet Operations</h2>';
$current_step++;
update_progress($current_step, $total_steps);

if (class_exists('Farmfaucet_Logger')) {
    try {
        // Test faucet creation
        $test_data = [
            'name' => 'Comprehensive Test Faucet',
            'faucet_type' => 'stage',
            'currency' => 'LTC',
            'amount' => '0.001',
            'cooldown' => 3600,
            'shortcode' => 'comprehensive_test_' . time(),
            'faucet_color' => 'green',
            'is_enabled' => 1
        ];
        
        $faucet_id = Farmfaucet_Logger::create_faucet($test_data);
        
        if ($faucet_id) {
            echo '<div class="success">✅ Faucet creation test passed</div>';
            
            // Test faucet retrieval
            $faucet = Farmfaucet_Logger::get_faucet($faucet_id);
            if ($faucet) {
                echo '<div class="success">✅ Faucet retrieval test passed</div>';
                
                // Test faucet update
                $update_result = Farmfaucet_Logger::update_faucet($faucet_id, ['name' => 'Updated Test Faucet']);
                if ($update_result) {
                    echo '<div class="success">✅ Faucet update test passed</div>';
                } else {
                    echo '<div class="warning">⚠️ Faucet update test failed</div>';
                }
                
                // Clean up
                Farmfaucet_Logger::delete_faucet($faucet_id);
                echo '<div class="info">ℹ️ Test faucet cleaned up</div>';
                
                $fix_results['faucet_operations'] = 'success';
            } else {
                echo '<div class="error">❌ Faucet retrieval test failed</div>';
                $fix_results['faucet_operations'] = 'failed';
                $all_fixes_successful = false;
            }
        } else {
            echo '<div class="error">❌ Faucet creation test failed</div>';
            $fix_results['faucet_operations'] = 'failed';
            $all_fixes_successful = false;
        }
    } catch (Exception $e) {
        echo '<div class="error">❌ Faucet operations test failed: ' . esc_html($e->getMessage()) . '</div>';
        $fix_results['faucet_operations'] = 'failed';
        $all_fixes_successful = false;
    }
} else {
    echo '<div class="error">❌ Farmfaucet_Logger class not available</div>';
    $fix_results['faucet_operations'] = 'failed';
    $all_fixes_successful = false;
}
echo '</div>';

// Step 6: Final summary
echo '<div class="step"><h2>Step 6: Final Summary</h2>';
$current_step++;
update_progress($current_step, $total_steps);

if ($all_fixes_successful) {
    echo '<div class="success"><h2>🎉 All Fixes Applied Successfully!</h2>';
    echo '<p>The Farm Faucet plugin has been completely fixed and is ready to use.</p>';
    echo '<p><strong>What was fixed:</strong></p>';
    echo '<ul>';
    echo '<li>✅ All required database tables and columns created</li>';
    echo '<li>✅ All database updater classes implemented</li>';
    echo '<li>✅ Plugin initialization components re-enabled</li>';
    echo '<li>✅ Faucet creation, updating, and deletion working</li>';
    echo '<li>✅ All appearance settings columns available</li>';
    echo '</ul>';
} else {
    echo '<div class="warning"><h2>⚠️ Fixes Applied with Some Issues</h2>';
    echo '<p>Most fixes were applied successfully, but some issues remain.</p>';
    echo '<p><strong>Fix Results:</strong></p>';
    echo '<ul>';
    foreach ($fix_results as $fix => $result) {
        $icon = $result === 'success' ? '✅' : ($result === 'partial' ? '⚠️' : '❌');
        echo '<li>' . $icon . ' ' . ucwords(str_replace('_', ' ', $fix)) . ': ' . $result . '</li>';
    }
    echo '</ul>';
}

echo '<div class="info"><h3>📋 Next Steps:</h3>';
echo '<ol>';
echo '<li><a href="' . admin_url('admin.php?page=farmfaucet') . '" class="button">Go to Farm Faucet Admin</a></li>';
echo '<li><a href="validate-plugin-fixes.php" class="button">Run Detailed Validation</a></li>';
echo '<li>Test creating a new faucet</li>';
echo '<li>Test appearance settings</li>';
echo '<li>Test currency creation</li>';
echo '</ol></div>';

echo '</div>';

echo '</body></html>';
?>
