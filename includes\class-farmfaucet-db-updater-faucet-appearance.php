<?php

/**
 * Farmfaucet Database Updater - Faucet Appearance
 *
 * Updates database to support comprehensive faucet appearance customization
 */
class Farmfaucet_DB_Updater_Faucet_Appearance
{
    /**
     * Run the faucet appearance update
     *
     * @return void
     */
    public static function run_update()
    {
        global $wpdb;
        $faucets_table = $wpdb->prefix . 'farmfaucet_faucets';

        // Check if faucets table exists
        $faucets_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$faucets_table}'") === $faucets_table;
        
        if (!$faucets_table_exists) {
            return;
        }

        // Get existing columns
        $columns = $wpdb->get_results("SHOW COLUMNS FROM {$faucets_table}");
        $column_names = array_map(function ($col) {
            return $col->Field;
        }, $columns);

        // Add comprehensive appearance columns
        $appearance_columns = [
            // Basic faucet appearance
            'faucet_type' => "varchar(20) NOT NULL DEFAULT 'stage'",
            'faucet_color' => "varchar(50) DEFAULT 'green' NOT NULL",
            'is_enabled' => "tinyint(1) NOT NULL DEFAULT 1",
            
            // Background styling
            'transparent_bg' => "tinyint(1) NOT NULL DEFAULT 0",
            'bg_style' => "varchar(20) NOT NULL DEFAULT 'solid'",
            'bg_color' => "varchar(50) NOT NULL DEFAULT '#f8fff8'",
            'bg_gradient_start' => "varchar(50) NOT NULL DEFAULT '#f8fff8'",
            'bg_gradient_end' => "varchar(50) NOT NULL DEFAULT '#e8f5e9'",
            
            // Text styling
            'text_color' => "varchar(50) NOT NULL DEFAULT '#4CAF50'",
            'text_shadow' => "varchar(50) NOT NULL DEFAULT 'none'",
            'text_align' => "varchar(20) DEFAULT 'center'",
            'font_family' => "varchar(100) DEFAULT 'inherit'",
            'font_size' => "varchar(20) DEFAULT '14px'",
            'font_weight' => "varchar(20) DEFAULT 'normal'",
            
            // Button styling
            'button_color' => "varchar(50) NOT NULL DEFAULT '#4CAF50'",
            'button_text_color' => "varchar(50) DEFAULT '#ffffff'",
            'button_hover_color' => "varchar(50) DEFAULT '#45a049'",
            'button_border_radius' => "varchar(20) NOT NULL DEFAULT '25px'",
            'button_padding' => "varchar(30) DEFAULT '10px 20px'",
            'button_margin' => "varchar(30) DEFAULT '10px 0'",
            'button_box_shadow' => "varchar(100) DEFAULT 'none'",
            
            // Border styling
            'border_color' => "varchar(50) NOT NULL DEFAULT '#4CAF50'",
            'border_radius' => "varchar(20) NOT NULL DEFAULT '8px'",
            'border_width' => "varchar(10) DEFAULT '1px'",
            'border_style' => "varchar(20) DEFAULT 'solid'",
            
            // Input field styling
            'input_label_color' => "varchar(50) NOT NULL DEFAULT '#333333'",
            'input_placeholder_color' => "varchar(50) NOT NULL DEFAULT '#999999'",
            'input_text_color' => "varchar(50) DEFAULT '#333333'",
            'input_bg_color' => "varchar(50) DEFAULT '#ffffff'",
            'input_border_color' => "varchar(50) DEFAULT '#cccccc'",
            'input_border_radius' => "varchar(20) DEFAULT '4px'",
            'input_padding' => "varchar(30) DEFAULT '8px 12px'",
            'input_margin' => "varchar(30) DEFAULT '5px 0'",
            
            // Container styling
            'container_width' => "varchar(20) DEFAULT 'auto'",
            'container_max_width' => "varchar(20) DEFAULT '500px'",
            'container_padding' => "varchar(30) DEFAULT '20px'",
            'container_margin' => "varchar(30) DEFAULT '20px auto'",
            'container_box_shadow' => "varchar(100) DEFAULT '0 2px 4px rgba(0,0,0,0.1)'",
            
            // Animation and effects
            'hover_effects' => "tinyint(1) DEFAULT 1",
            'transition_duration' => "varchar(10) DEFAULT '0.3s'",
            'animation_type' => "varchar(20) DEFAULT 'none'",
            
            // Custom CSS
            'custom_css' => "longtext DEFAULT NULL",
            'custom_js' => "longtext DEFAULT NULL"
        ];

        // Add columns that don't exist
        foreach ($appearance_columns as $column => $definition) {
            if (!in_array($column, $column_names)) {
                $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN {$column} {$definition}");
            }
        }

        // Add type-specific columns for different faucet types
        $type_specific_columns = [
            // For dummy and withdrawal faucets
            'currency_id' => "bigint(20) DEFAULT 0",
            'min_withdrawal' => "varchar(50) DEFAULT '0'",
            'available_currencies' => "longtext DEFAULT NULL",
            'conversion_currencies' => "longtext DEFAULT NULL",
            'view_style' => "varchar(20) DEFAULT 'default'",
            'ads_only_conversion' => "tinyint(1) DEFAULT 0",
            
            // For conversion faucets
            'conversion_rate_display' => "tinyint(1) DEFAULT 1",
            'conversion_fee_percentage' => "decimal(5,2) DEFAULT 0.00",
            'min_conversion_amount' => "varchar(50) DEFAULT '0'",
            'max_conversion_amount' => "varchar(50) DEFAULT '0'",
            
            // For withdrawal faucets
            'withdrawal_fee_percentage' => "decimal(5,2) DEFAULT 0.00",
            'withdrawal_processing_time' => "varchar(50) DEFAULT 'instant'",
            'withdrawal_limits_per_day' => "int(11) DEFAULT 0"
        ];

        foreach ($type_specific_columns as $column => $definition) {
            if (!in_array($column, $column_names)) {
                $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN {$column} {$definition}");
            }
        }

        // Add indexes for performance
        $appearance_indexes = [
            'faucet_type',
            'is_enabled',
            'faucet_color',
            'transparent_bg',
            'bg_style'
        ];

        foreach ($appearance_indexes as $index_column) {
            if (in_array($index_column, $column_names)) {
                $indexes = $wpdb->get_results("SHOW INDEX FROM {$faucets_table} WHERE Key_name = '{$index_column}'");
                if (empty($indexes)) {
                    $wpdb->query("ALTER TABLE {$faucets_table} ADD INDEX {$index_column} ({$index_column})");
                }
            }
        }

        // Update existing faucets with default appearance values
        $default_updates = [
            'faucet_type' => 'stage',
            'faucet_color' => 'green',
            'is_enabled' => 1,
            'transparent_bg' => 0,
            'bg_style' => 'solid',
            'bg_color' => '#f8fff8',
            'text_color' => '#4CAF50',
            'button_color' => '#4CAF50',
            'border_color' => '#4CAF50'
        ];

        foreach ($default_updates as $column => $default_value) {
            if (in_array($column, $column_names)) {
                $wpdb->query($wpdb->prepare(
                    "UPDATE {$faucets_table} SET {$column} = %s WHERE {$column} IS NULL OR {$column} = ''",
                    $default_value
                ));
            }
        }
    }
}
