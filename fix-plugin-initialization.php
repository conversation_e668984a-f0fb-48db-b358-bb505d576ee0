<?php
/**
 * Farm Faucet Plugin Initialization Fix
 * 
 * This script re-enables the disabled components in the main plugin file
 * after the database schema has been fixed.
 */

// Load WordPress
$wp_load_path = dirname(__FILE__) . '/../../../wp-load.php';
if (file_exists($wp_load_path)) {
    require_once($wp_load_path);
} else {
    die('WordPress not found. Please run this script from the plugin directory.');
}

// Security check
if (!function_exists('current_user_can') || !current_user_can('manage_options')) {
    die('You do not have permission to run this script.');
}

echo '<!DOCTYPE html>
<html>
<head>
    <title>Farm Faucet Plugin Initialization Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { background: #d4edda; color: #155724; padding: 10px; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 10px; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 10px; border: 1px solid #ffeaa7; border-radius: 4px; margin: 10px 0; }
        h1, h2 { color: #333; }
        .step { margin: 20px 0; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>';

echo '<h1>🔧 Farm Faucet Plugin Initialization Fix</h1>';

$plugin_file = dirname(__FILE__) . '/farmfaucet.php';
$backup_file = dirname(__FILE__) . '/farmfaucet.php.backup';

// Step 1: Create backup
echo '<div class="step"><h2>Step 1: Creating Backup</h2>';
if (file_exists($plugin_file)) {
    if (copy($plugin_file, $backup_file)) {
        echo '<div class="success">✅ Backup created: farmfaucet.php.backup</div>';
    } else {
        echo '<div class="error">❌ Failed to create backup</div>';
        echo '</body></html>';
        exit;
    }
} else {
    echo '<div class="error">❌ Plugin file not found: ' . $plugin_file . '</div>';
    echo '</body></html>';
    exit;
}
echo '</div>';

// Step 2: Read current file
echo '<div class="step"><h2>Step 2: Analyzing Current Plugin File</h2>';
$content = file_get_contents($plugin_file);

if ($content === false) {
    echo '<div class="error">❌ Failed to read plugin file</div>';
    echo '</body></html>';
    exit;
}

// Check for disabled components
$disabled_components = [
    'Settings Manager' => '// Settings manager temporarily disabled',
    'Database Updater' => '// Database updater temporarily disabled',
    'Installation Check' => '// Installation check temporarily disabled'
];

$found_disabled = [];
foreach ($disabled_components as $name => $marker) {
    if (strpos($content, $marker) !== false) {
        $found_disabled[] = $name;
    }
}

if (!empty($found_disabled)) {
    echo '<div class="warning">⚠️ Found disabled components: ' . implode(', ', $found_disabled) . '</div>';
} else {
    echo '<div class="info">ℹ️ No disabled components found (they may already be enabled)</div>';
}
echo '</div>';

// Step 3: Fix the plugin file
echo '<div class="step"><h2>Step 3: Re-enabling Components</h2>';

$fixes_applied = [];

// Fix 1: Re-enable Settings Manager
$old_settings_manager = '        // Settings manager temporarily disabled to prevent hanging
        // Using direct save method in admin class instead
        /*
        try {
            if (class_exists(\'Farmfaucet_Settings_Manager\')) {
                Farmfaucet_Settings_Manager::init();
            } else {
                error_log(\'Farmfaucet_Settings_Manager class not found\');
            }
        } catch (Exception $e) {
            error_log(\'Farmfaucet settings manager initialization error: \' . $e->getMessage());
        } catch (Error $e) {
            error_log(\'Farmfaucet settings manager initialization fatal error: \' . $e->getMessage());
        }
        */';

$new_settings_manager = '        // Initialize Settings Manager
        try {
            if (class_exists(\'Farmfaucet_Settings_Manager\')) {
                Farmfaucet_Settings_Manager::init();
            } else {
                error_log(\'Farmfaucet_Settings_Manager class not found\');
            }
        } catch (Exception $e) {
            error_log(\'Farmfaucet settings manager initialization error: \' . $e->getMessage());
        } catch (Error $e) {
            error_log(\'Farmfaucet settings manager initialization fatal error: \' . $e->getMessage());
        }';

if (strpos($content, $old_settings_manager) !== false) {
    $content = str_replace($old_settings_manager, $new_settings_manager, $content);
    $fixes_applied[] = 'Re-enabled Settings Manager';
    echo '<div class="success">✅ Re-enabled Settings Manager</div>';
}

// Fix 2: Re-enable Database Updater
$old_db_updater = '        // Database updater temporarily disabled to prevent hanging
        // TODO: Re-enable after fixing hanging issue
        /*
        try {
            if (class_exists(\'Farmfaucet_DB_Updater\')) {
                // Run database updates
                Farmfaucet_DB_Updater::run_updates(true);
            } else {
                error_log(\'Farmfaucet_DB_Updater class not found\');
            }
        } catch (Exception $e) {
            error_log(\'Farmfaucet database updater initialization error: \' . $e->getMessage());
        } catch (Error $e) {
            error_log(\'Farmfaucet database updater initialization fatal error: \' . $e->getMessage());
        }
        */';

$new_db_updater = '        // Initialize Database Updater
        try {
            if (class_exists(\'Farmfaucet_DB_Updater\')) {
                // Run database updates
                Farmfaucet_DB_Updater::run_updates(true);
            } else {
                error_log(\'Farmfaucet_DB_Updater class not found\');
            }
        } catch (Exception $e) {
            error_log(\'Farmfaucet database updater initialization error: \' . $e->getMessage());
        } catch (Error $e) {
            error_log(\'Farmfaucet database updater initialization fatal error: \' . $e->getMessage());
        }';

if (strpos($content, $old_db_updater) !== false) {
    $content = str_replace($old_db_updater, $new_db_updater, $content);
    $fixes_applied[] = 'Re-enabled Database Updater';
    echo '<div class="success">✅ Re-enabled Database Updater</div>';
}

// Fix 3: Re-enable Installation Check
$old_install_check = '    // Installation check temporarily disabled to prevent hanging
    // TODO: Re-enable after fixing hanging issue
    /*
    try {
        if (class_exists(\'Farmfaucet_Installer\')) {
            Farmfaucet_Installer::check_and_install();
        } else {
            error_log(\'Farmfaucet_Installer class not found\');
        }
    } catch (Exception $e) {
        error_log(\'Farmfaucet installation check error: \' . $e->getMessage());
    } catch (Error $e) {
        error_log(\'Farmfaucet installation check fatal error: \' . $e->getMessage());
    }
    */';

$new_install_check = '    // Initialize Installation Check
    try {
        if (class_exists(\'Farmfaucet_Installer\')) {
            Farmfaucet_Installer::check_and_install();
        } else {
            error_log(\'Farmfaucet_Installer class not found\');
        }
    } catch (Exception $e) {
        error_log(\'Farmfaucet installation check error: \' . $e->getMessage());
    } catch (Error $e) {
        error_log(\'Farmfaucet installation check fatal error: \' . $e->getMessage());
    }';

if (strpos($content, $old_install_check) !== false) {
    $content = str_replace($old_install_check, $new_install_check, $content);
    $fixes_applied[] = 'Re-enabled Installation Check';
    echo '<div class="success">✅ Re-enabled Installation Check</div>';
}

echo '</div>';

// Step 4: Write the fixed file
echo '<div class="step"><h2>Step 4: Saving Fixed Plugin File</h2>';

if (!empty($fixes_applied)) {
    if (file_put_contents($plugin_file, $content) !== false) {
        echo '<div class="success">✅ Successfully updated plugin file</div>';
        
        echo '<div class="success"><h3>✅ Fixes Applied:</h3><ul>';
        foreach ($fixes_applied as $fix) {
            echo '<li>' . esc_html($fix) . '</li>';
        }
        echo '</ul></div>';
        
        echo '<div class="info"><h3>📋 Next Steps:</h3>';
        echo '<ol>';
        echo '<li>Test the plugin functionality</li>';
        echo '<li>Try creating a new faucet</li>';
        echo '<li>Test settings saving</li>';
        echo '<li>Verify currency creation works</li>';
        echo '<li>If issues persist, restore from backup: farmfaucet.php.backup</li>';
        echo '</ol></div>';
        
    } else {
        echo '<div class="error">❌ Failed to write updated plugin file</div>';
    }
} else {
    echo '<div class="info">ℹ️ No changes needed - components may already be enabled</div>';
}

echo '</div>';

echo '<p><a href="' . admin_url('admin.php?page=farmfaucet') . '" class="button button-primary">Return to Farm Faucet Admin</a></p>';

echo '</body></html>';
?>
