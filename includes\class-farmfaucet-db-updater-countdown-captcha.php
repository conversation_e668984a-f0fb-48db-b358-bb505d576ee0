<?php

/**
 * Farmfaucet Database Updater - Countdown Captcha
 *
 * Updates database to support countdown and captcha features
 */
class Farmfaucet_DB_Updater_Countdown_Captcha
{
    /**
     * Run the countdown captcha update
     *
     * @return void
     */
    public static function run_update()
    {
        global $wpdb;
        $buttons_table = $wpdb->prefix . 'farmfaucet_buttons';
        $faucets_table = $wpdb->prefix . 'farmfaucet_faucets';

        // Update buttons table for countdown features
        $buttons_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$buttons_table}'") === $buttons_table;
        
        if ($buttons_table_exists) {
            // Get existing columns
            $columns = $wpdb->get_results("SHOW COLUMNS FROM {$buttons_table}");
            $column_names = array_map(function ($col) {
                return $col->Field;
            }, $columns);

            // Add countdown columns if they don't exist
            $countdown_columns = [
                'countdown_enabled' => "tinyint(1) NOT NULL DEFAULT 0",
                'countdown_seconds' => "int(11) NOT NULL DEFAULT 60",
                'countdown_message' => "varchar(255) DEFAULT '' NOT NULL",
                'countdown_click_activation' => "tinyint(1) NOT NULL DEFAULT 0",
                'countdown_click_element_id' => "varchar(50) DEFAULT '' NOT NULL",
                'countdown_pre_click_message' => "varchar(255) DEFAULT '' NOT NULL",
                'countdown_standby' => "tinyint(1) NOT NULL DEFAULT 0"
            ];

            foreach ($countdown_columns as $column => $definition) {
                if (!in_array($column, $column_names)) {
                    $wpdb->query("ALTER TABLE {$buttons_table} ADD COLUMN {$column} {$definition}");
                }
            }

            // Add indexes for countdown columns
            $indexes = $wpdb->get_results("SHOW INDEX FROM {$buttons_table} WHERE Key_name = 'countdown_enabled'");
            if (empty($indexes)) {
                $wpdb->query("ALTER TABLE {$buttons_table} ADD INDEX countdown_enabled (countdown_enabled)");
            }
        }

        // Update faucets table for captcha features
        $faucets_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$faucets_table}'") === $faucets_table;
        
        if ($faucets_table_exists) {
            // Get existing columns
            $columns = $wpdb->get_results("SHOW COLUMNS FROM {$faucets_table}");
            $column_names = array_map(function ($col) {
                return $col->Field;
            }, $columns);

            // Ensure captcha_type column exists and has proper size
            if (!in_array('captcha_type', $column_names)) {
                $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN captcha_type varchar(20) DEFAULT '' NOT NULL");
            } else {
                // Update column size if it exists
                $wpdb->query("ALTER TABLE {$faucets_table} MODIFY COLUMN captcha_type varchar(20) DEFAULT '' NOT NULL");
            }

            // Add index for captcha_type
            $indexes = $wpdb->get_results("SHOW INDEX FROM {$faucets_table} WHERE Key_name = 'captcha_type'");
            if (empty($indexes)) {
                $wpdb->query("ALTER TABLE {$faucets_table} ADD INDEX captcha_type (captcha_type)");
            }
        }

        // Create captcha settings table if it doesn't exist
        $captcha_table = $wpdb->prefix . 'farmfaucet_captcha_settings';
        $captcha_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$captcha_table}'") === $captcha_table;

        if (!$captcha_table_exists) {
            $charset_collate = $wpdb->get_charset_collate();
            $sql = "CREATE TABLE {$captcha_table} (
                id mediumint(9) NOT NULL AUTO_INCREMENT,
                captcha_type varchar(20) NOT NULL,
                site_key varchar(255) DEFAULT '' NOT NULL,
                secret_key varchar(255) DEFAULT '' NOT NULL,
                is_active tinyint(1) NOT NULL DEFAULT 0,
                created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
                updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
                PRIMARY KEY (id),
                UNIQUE KEY captcha_type (captcha_type),
                KEY is_active (is_active)
            ) {$charset_collate};";

            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
        }
    }
}
