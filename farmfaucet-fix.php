<?php
/**
 * Farm Faucet Settings Fix
 * Direct WordPress admin page to fix settings save and transaction display
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Add admin menu
add_action('admin_menu', 'farmfaucet_fix_admin_menu');

function farmfaucet_fix_admin_menu() {
    add_menu_page(
        'Farm Faucet Fix',
        'Farm Faucet Fix',
        'manage_options',
        'farmfaucet-fix',
        'farmfaucet_fix_admin_page',
        'dashicons-money-alt',
        30
    );
}

// Handle settings save
add_action('admin_init', 'farmfaucet_fix_handle_save');

function farmfaucet_fix_handle_save() {
    if (isset($_POST['farmfaucet_fix_save']) && check_admin_referer('farmfaucet_fix_save')) {
        $settings = [
            'farmfaucet_captcha_type',
            'farmfaucet_hcaptcha_sitekey',
            'farmfaucet_hcaptcha_secret',
            'farmfaucet_recaptcha_sitekey',
            'farmfaucet_recaptcha_secret',
            'farmfaucet_turnstile_sitekey',
            'farmfaucet_turnstile_secret',
            'farmfaucet_faucetpay_api',
            'farmfaucet_redirect_url',
            'farmfaucet_daily_reset',
            'farmfaucet_leaderboard_reset_date'
        ];
        
        $saved = 0;
        foreach ($settings as $setting) {
            if (isset($_POST[$setting])) {
                $value = sanitize_text_field($_POST[$setting]);
                if ($setting === 'farmfaucet_redirect_url') {
                    $value = esc_url_raw($value);
                }
                update_option($setting, $value);
                $saved++;
            }
        }
        
        add_action('admin_notices', function() use ($saved) {
            echo '<div class="notice notice-success is-dismissible">';
            echo '<p><strong>Settings saved successfully!</strong> (' . $saved . ' settings updated)</p>';
            echo '</div>';
        });
    }
}

// Admin page content
function farmfaucet_fix_admin_page() {
    ?>
    <div class="wrap">
        <h1>🚀 Farm Faucet Settings Fix</h1>
        
        <div style="background: #e8f5e9; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #4CAF50;">
            <h3 style="color: #2e7d32; margin-top: 0;">✅ Working Settings Save</h3>
            <p style="color: #2e7d32;">This page provides a working settings save mechanism while the main plugin issues are resolved.</p>
        </div>
        
        <form method="post" action="">
            <?php wp_nonce_field('farmfaucet_fix_save'); ?>
            
            <table class="form-table">
                <tr>
                    <th scope="row">Captcha Type</th>
                    <td>
                        <select name="farmfaucet_captcha_type">
                            <?php $current = get_option('farmfaucet_captcha_type', 'hcaptcha'); ?>
                            <option value="hcaptcha" <?php selected($current, 'hcaptcha'); ?>>hCaptcha</option>
                            <option value="recaptcha" <?php selected($current, 'recaptcha'); ?>>Google reCAPTCHA</option>
                            <option value="turnstile" <?php selected($current, 'turnstile'); ?>>Cloudflare Turnstile</option>
                        </select>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">hCaptcha Site Key</th>
                    <td>
                        <input type="text" name="farmfaucet_hcaptcha_sitekey" value="<?php echo esc_attr(get_option('farmfaucet_hcaptcha_sitekey', '')); ?>" class="regular-text" />
                        <p class="description">Your hCaptcha site key (public key)</p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">hCaptcha Secret Key</th>
                    <td>
                        <input type="password" name="farmfaucet_hcaptcha_secret" value="<?php echo esc_attr(get_option('farmfaucet_hcaptcha_secret', '')); ?>" class="regular-text" />
                        <p class="description">Your hCaptcha secret key (private key)</p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">reCAPTCHA Site Key</th>
                    <td>
                        <input type="text" name="farmfaucet_recaptcha_sitekey" value="<?php echo esc_attr(get_option('farmfaucet_recaptcha_sitekey', '')); ?>" class="regular-text" />
                        <p class="description">Your Google reCAPTCHA site key</p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">reCAPTCHA Secret Key</th>
                    <td>
                        <input type="password" name="farmfaucet_recaptcha_secret" value="<?php echo esc_attr(get_option('farmfaucet_recaptcha_secret', '')); ?>" class="regular-text" />
                        <p class="description">Your Google reCAPTCHA secret key</p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">Turnstile Site Key</th>
                    <td>
                        <input type="text" name="farmfaucet_turnstile_sitekey" value="<?php echo esc_attr(get_option('farmfaucet_turnstile_sitekey', '')); ?>" class="regular-text" />
                        <p class="description">Your Cloudflare Turnstile site key</p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">Turnstile Secret Key</th>
                    <td>
                        <input type="password" name="farmfaucet_turnstile_secret" value="<?php echo esc_attr(get_option('farmfaucet_turnstile_secret', '')); ?>" class="regular-text" />
                        <p class="description">Your Cloudflare Turnstile secret key</p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">FaucetPay API Key</th>
                    <td>
                        <input type="password" name="farmfaucet_faucetpay_api" value="<?php echo esc_attr(get_option('farmfaucet_faucetpay_api', '')); ?>" class="regular-text" />
                        <p class="description">Your FaucetPay API key for payments</p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">Redirect URL</th>
                    <td>
                        <input type="url" name="farmfaucet_redirect_url" value="<?php echo esc_attr(get_option('farmfaucet_redirect_url', '')); ?>" class="regular-text" />
                        <p class="description">URL to redirect after successful claim (optional)</p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">Daily Reset Time</th>
                    <td>
                        <input type="time" name="farmfaucet_daily_reset" value="<?php echo esc_attr(get_option('farmfaucet_daily_reset', '00:00')); ?>" />
                        <p class="description">Time when daily limits reset</p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">Leaderboard Reset Date</th>
                    <td>
                        <input type="date" name="farmfaucet_leaderboard_reset_date" value="<?php echo esc_attr(get_option('farmfaucet_leaderboard_reset_date', '')); ?>" />
                        <p class="description">Date when leaderboard resets (optional)</p>
                    </td>
                </tr>
            </table>
            
            <?php submit_button('Save Settings', 'primary', 'farmfaucet_fix_save'); ?>
        </form>
        
        <div style="background: #f0f7ff; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #2196F3;">
            <h3 style="color: #1976d2; margin-top: 0;">📊 Transaction Display Fix</h3>
            <p style="color: #1976d2;">To fix the transaction display, add this to your theme's functions.php or create a separate plugin:</p>
            <pre style="background: #fff; padding: 15px; border-radius: 4px; overflow-x: auto;">
// Fix transaction tab display
add_action('wp_footer', function() {
    if (is_admin()) {
        ?>
        &lt;script&gt;
        jQuery(document).ready(function($) {
            // Fix transaction tab switching
            $('.nav-tab').on('click', function(e) {
                if ($(this).attr('href').includes('tab=faucets')) {
                    // Show transaction content
                    setTimeout(function() {
                        $('.transaction-log-section').show();
                        $('#transaction-tabs').tabs();
                    }, 100);
                }
            });
            
            // Fix transaction filtering
            $(document).on('click', '.transaction-subtab', function() {
                var type = $(this).data('type');
                var container = $(this).closest('.transaction-container').find('.transaction-chat-box');
                
                // Hide all messages
                container.find('.transaction-message').hide();
                
                // Show filtered messages
                if (type === 'all') {
                    container.find('.transaction-message').show();
                } else {
                    container.find('.transaction-message[data-type="' + type + '"]').show();
                }
                
                // Update active tab
                $(this).siblings().removeClass('active');
                $(this).addClass('active');
            });
        });
        &lt;/script&gt;
        &lt;?php
    }
});
            </pre>
        </div>
        
        <div style="background: #fff3e0; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #ff9800;">
            <h3 style="color: #f57c00; margin-top: 0;">🔧 Next Steps</h3>
            <div style="color: #f57c00;">
                <ol>
                    <li><strong>Use this page to save settings</strong> - This works reliably</li>
                    <li><strong>Add the JavaScript fix</strong> - Copy the code above to fix transaction display</li>
                    <li><strong>Test the fixes</strong> - Verify settings save and transaction filtering work</li>
                    <li><strong>Gradually re-enable features</strong> - Once core functionality works, add other features back</li>
                </ol>
            </div>
        </div>
    </div>
    <?php
}
?>
