<?php

/**
 * Farmfaucet Database Updater - Form Background
 *
 * Updates database to support form background customization
 */
class Farmfaucet_DB_Updater_Form_Bg
{
    /**
     * Run the form background update
     *
     * @return void
     */
    public static function run_update()
    {
        global $wpdb;
        $faucets_table = $wpdb->prefix . 'farmfaucet_faucets';

        // Check if faucets table exists
        $faucets_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$faucets_table}'") === $faucets_table;
        
        if (!$faucets_table_exists) {
            return;
        }

        // Get existing columns
        $columns = $wpdb->get_results("SHOW COLUMNS FROM {$faucets_table}");
        $column_names = array_map(function ($col) {
            return $col->Field;
        }, $columns);

        // Add form background columns if they don't exist
        $form_bg_columns = [
            'form_bg_color' => "varchar(50) NOT NULL DEFAULT '#ffffff'",
            'form_transparent' => "tinyint(1) NOT NULL DEFAULT 0"
        ];

        foreach ($form_bg_columns as $column => $definition) {
            if (!in_array($column, $column_names)) {
                // Determine where to add the column (after border_radius if it exists)
                $after_column = in_array('border_radius', $column_names) ? 'AFTER border_radius' : '';
                $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN {$column} {$definition} {$after_column}");
            }
        }

        // Update existing faucets to set default form background values
        $wpdb->query("UPDATE {$faucets_table} SET form_bg_color = '#ffffff', form_transparent = 0 WHERE form_bg_color IS NULL OR form_bg_color = ''");

        // Add additional form styling columns
        $additional_form_columns = [
            'form_border_color' => "varchar(50) DEFAULT '#cccccc'",
            'form_border_width' => "varchar(10) DEFAULT '1px'",
            'form_border_style' => "varchar(20) DEFAULT 'solid'",
            'form_border_radius' => "varchar(20) DEFAULT '4px'",
            'form_padding' => "varchar(20) DEFAULT '20px'",
            'form_margin' => "varchar(20) DEFAULT '10px'",
            'form_box_shadow' => "varchar(100) DEFAULT 'none'"
        ];

        foreach ($additional_form_columns as $column => $definition) {
            if (!in_array($column, $column_names)) {
                $after_column = in_array('form_transparent', $column_names) ? 'AFTER form_transparent' : '';
                $wpdb->query("ALTER TABLE {$faucets_table} ADD COLUMN {$column} {$definition} {$after_column}");
            }
        }

        // Add indexes for form styling columns
        $form_indexes = [
            'form_transparent',
            'form_bg_color'
        ];

        foreach ($form_indexes as $index_column) {
            if (in_array($index_column, $column_names)) {
                $indexes = $wpdb->get_results("SHOW INDEX FROM {$faucets_table} WHERE Key_name = '{$index_column}'");
                if (empty($indexes)) {
                    $wpdb->query("ALTER TABLE {$faucets_table} ADD INDEX {$index_column} ({$index_column})");
                }
            }
        }

        // Create form templates table for predefined form styles
        $form_templates_table = $wpdb->prefix . 'farmfaucet_form_templates';
        $templates_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$form_templates_table}'") === $form_templates_table;

        if (!$templates_table_exists) {
            $charset_collate = $wpdb->get_charset_collate();
            $sql = "CREATE TABLE {$form_templates_table} (
                id mediumint(9) NOT NULL AUTO_INCREMENT,
                template_name varchar(100) NOT NULL,
                template_description text DEFAULT NULL,
                form_bg_color varchar(50) NOT NULL DEFAULT '#ffffff',
                form_transparent tinyint(1) NOT NULL DEFAULT 0,
                form_border_color varchar(50) DEFAULT '#cccccc',
                form_border_width varchar(10) DEFAULT '1px',
                form_border_style varchar(20) DEFAULT 'solid',
                form_border_radius varchar(20) DEFAULT '4px',
                form_padding varchar(20) DEFAULT '20px',
                form_margin varchar(20) DEFAULT '10px',
                form_box_shadow varchar(100) DEFAULT 'none',
                is_default tinyint(1) NOT NULL DEFAULT 0,
                created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
                updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
                PRIMARY KEY (id),
                UNIQUE KEY template_name (template_name),
                KEY is_default (is_default)
            ) {$charset_collate};";

            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);

            // Insert default form templates
            $default_templates = [
                [
                    'template_name' => 'Default',
                    'template_description' => 'Standard form with white background',
                    'form_bg_color' => '#ffffff',
                    'form_transparent' => 0,
                    'is_default' => 1
                ],
                [
                    'template_name' => 'Transparent',
                    'template_description' => 'Fully transparent form background',
                    'form_bg_color' => '#ffffff',
                    'form_transparent' => 1,
                    'is_default' => 0
                ],
                [
                    'template_name' => 'Dark Theme',
                    'template_description' => 'Dark background form',
                    'form_bg_color' => '#2c3e50',
                    'form_transparent' => 0,
                    'form_border_color' => '#34495e',
                    'is_default' => 0
                ]
            ];

            foreach ($default_templates as $template) {
                $wpdb->insert($form_templates_table, $template);
            }
        }
    }
}
