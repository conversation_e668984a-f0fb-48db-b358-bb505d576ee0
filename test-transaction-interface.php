<?php
/**
 * Test Script for Modern Transaction Interface
 * 
 * This script tests the new user-centric transaction interface
 * and validates all functionality works correctly.
 */

// Load WordPress
$wp_load_path = dirname(__FILE__) . '/../../../wp-load.php';
if (file_exists($wp_load_path)) {
    require_once($wp_load_path);
} else {
    die('WordPress not found. Please run this script from the plugin directory.');
}

// Security check
if (!function_exists('current_user_can') || !current_user_can('manage_options')) {
    die('You do not have permission to run this script.');
}

echo '<!DOCTYPE html>
<html>
<head>
    <title>Transaction Interface Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { background: #d4edda; color: #155724; padding: 10px; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 10px; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 10px; border: 1px solid #ffeaa7; border-radius: 4px; margin: 10px 0; }
        h1, h2 { color: #333; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .button { display: inline-block; padding: 10px 20px; background: #4CAF50; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
    </style>
</head>
<body>';

echo '<h1>🧪 Transaction Interface Test Suite</h1>';

$test_results = [];
$total_tests = 0;
$passed_tests = 0;

// Test 1: Database Tables Exist
echo '<div class="test-section"><h2>Test 1: Database Tables</h2>';
$total_tests++;

global $wpdb;
$required_tables = [
    'farmfaucet_users',
    'farmfaucet_claims',
    'farmfaucet_faucets',
    'farmfaucet_logs'
];

$missing_tables = [];
foreach ($required_tables as $table) {
    $full_table = $wpdb->prefix . $table;
    $exists = $wpdb->get_var("SHOW TABLES LIKE '{$full_table}'") === $full_table;
    if (!$exists) {
        $missing_tables[] = $table;
    }
}

if (empty($missing_tables)) {
    echo '<div class="success">✅ All required tables exist</div>';
    $passed_tests++;
    $test_results['database_tables'] = 'PASS';
} else {
    echo '<div class="error">❌ Missing tables: ' . implode(', ', $missing_tables) . '</div>';
    $test_results['database_tables'] = 'FAIL';
}
echo '</div>';

// Test 2: Sample Data Creation
echo '<div class="test-section"><h2>Test 2: Sample Data Creation</h2>';
$total_tests++;

try {
    // Create sample users
    $users_table = $wpdb->prefix . 'farmfaucet_users';
    $claims_table = $wpdb->prefix . 'farmfaucet_claims';
    $faucets_table = $wpdb->prefix . 'farmfaucet_faucets';
    
    // Create test users
    $test_users = [
        [
            'user_hash' => md5('test_user_1' . time()),
            'display_name' => 'Test User 1',
            'profile_picture' => '',
            'created_at' => current_time('mysql')
        ],
        [
            'user_hash' => md5('test_user_2' . time()),
            'display_name' => 'Test User 2',
            'profile_picture' => '',
            'created_at' => current_time('mysql')
        ]
    ];
    
    $created_users = [];
    foreach ($test_users as $user) {
        $result = $wpdb->insert($users_table, $user);
        if ($result) {
            $created_users[] = $user['user_hash'];
        }
    }
    
    // Create test faucet
    $test_faucet = [
        'name' => 'Test Faucet for Interface',
        'faucet_type' => 'stage',
        'currency' => 'LTC',
        'amount' => '0.001',
        'cooldown' => 3600,
        'shortcode' => 'test_interface_' . time(),
        'created_at' => current_time('mysql'),
        'updated_at' => current_time('mysql')
    ];
    
    $wpdb->insert($faucets_table, $test_faucet);
    $test_faucet_id = $wpdb->insert_id;
    
    // Create test claims
    if (!empty($created_users) && $test_faucet_id) {
        foreach ($created_users as $user_hash) {
            // Create successful claim
            $wpdb->insert($claims_table, [
                'user_hash' => $user_hash,
                'faucet_id' => $test_faucet_id,
                'timestamp' => current_time('mysql'),
                'amount' => '0.001',
                'currency' => 'LTC',
                'status' => 'success'
            ]);
            
            // Create failed claim
            $wpdb->insert($claims_table, [
                'user_hash' => $user_hash,
                'faucet_id' => $test_faucet_id,
                'timestamp' => date('Y-m-d H:i:s', strtotime('-1 hour')),
                'amount' => '0.001',
                'currency' => 'LTC',
                'status' => 'error'
            ]);
        }
    }
    
    echo '<div class="success">✅ Sample data created successfully</div>';
    echo '<div class="info">Created ' . count($created_users) . ' test users and ' . (count($created_users) * 2) . ' test transactions</div>';
    $passed_tests++;
    $test_results['sample_data'] = 'PASS';
    
} catch (Exception $e) {
    echo '<div class="error">❌ Failed to create sample data: ' . $e->getMessage() . '</div>';
    $test_results['sample_data'] = 'FAIL';
}
echo '</div>';

// Test 3: User Retrieval Function
echo '<div class="test-section"><h2>Test 3: User Retrieval Function</h2>';
$total_tests++;

try {
    // Test the get_users_with_transactions method
    if (class_exists('Farmfaucet_Admin')) {
        $admin = new Farmfaucet_Admin();
        $reflection = new ReflectionClass($admin);
        $method = $reflection->getMethod('get_users_with_transactions');
        $method->setAccessible(true);
        $users = $method->invoke($admin);
        
        if (is_array($users) && count($users) > 0) {
            echo '<div class="success">✅ User retrieval function works</div>';
            echo '<div class="info">Retrieved ' . count($users) . ' users with transactions</div>';
            
            // Display sample user data
            echo '<table>';
            echo '<tr><th>Display Name</th><th>User Hash</th><th>Transaction Count</th><th>Last Activity</th></tr>';
            foreach (array_slice($users, 0, 5) as $user) {
                echo '<tr>';
                echo '<td>' . esc_html($user['display_name'] ?: 'N/A') . '</td>';
                echo '<td>' . esc_html(substr($user['user_hash'], 0, 16)) . '...</td>';
                echo '<td>' . esc_html($user['transaction_count']) . '</td>';
                echo '<td>' . esc_html($user['last_activity'] ?: 'N/A') . '</td>';
                echo '</tr>';
            }
            echo '</table>';
            
            $passed_tests++;
            $test_results['user_retrieval'] = 'PASS';
        } else {
            echo '<div class="warning">⚠️ User retrieval function returned no data</div>';
            $test_results['user_retrieval'] = 'PARTIAL';
        }
    } else {
        echo '<div class="error">❌ Farmfaucet_Admin class not found</div>';
        $test_results['user_retrieval'] = 'FAIL';
    }
} catch (Exception $e) {
    echo '<div class="error">❌ User retrieval test failed: ' . $e->getMessage() . '</div>';
    $test_results['user_retrieval'] = 'FAIL';
}
echo '</div>';

// Test 4: AJAX Handlers
echo '<div class="test-section"><h2>Test 4: AJAX Handlers</h2>';
$total_tests++;

$ajax_handlers = [
    'farmfaucet_get_user_details',
    'farmfaucet_get_user_transactions',
    'farmfaucet_search_users'
];

$missing_handlers = [];
foreach ($ajax_handlers as $handler) {
    if (!has_action('wp_ajax_' . $handler)) {
        $missing_handlers[] = $handler;
    }
}

if (empty($missing_handlers)) {
    echo '<div class="success">✅ All AJAX handlers are registered</div>';
    $passed_tests++;
    $test_results['ajax_handlers'] = 'PASS';
} else {
    echo '<div class="error">❌ Missing AJAX handlers: ' . implode(', ', $missing_handlers) . '</div>';
    $test_results['ajax_handlers'] = 'FAIL';
}
echo '</div>';

// Test 5: CSS and JavaScript Files
echo '<div class="test-section"><h2>Test 5: Asset Files</h2>';
$total_tests++;

$css_file = dirname(__FILE__) . '/assets/css/admin.css';
$js_file = dirname(__FILE__) . '/assets/js/admin.js';

$css_exists = file_exists($css_file);
$js_exists = file_exists($js_file);

if ($css_exists && $js_exists) {
    // Check for modern transaction interface styles
    $css_content = file_get_contents($css_file);
    $js_content = file_get_contents($js_file);
    
    $css_has_modern_styles = strpos($css_content, 'modern-transaction-container') !== false;
    $js_has_modern_functions = strpos($js_content, 'initModernTransactionInterface') !== false;
    
    if ($css_has_modern_styles && $js_has_modern_functions) {
        echo '<div class="success">✅ Asset files exist and contain modern interface code</div>';
        $passed_tests++;
        $test_results['asset_files'] = 'PASS';
    } else {
        echo '<div class="warning">⚠️ Asset files exist but may be missing modern interface code</div>';
        $test_results['asset_files'] = 'PARTIAL';
    }
} else {
    echo '<div class="error">❌ Asset files missing - CSS: ' . ($css_exists ? 'OK' : 'MISSING') . ', JS: ' . ($js_exists ? 'OK' : 'MISSING') . '</div>';
    $test_results['asset_files'] = 'FAIL';
}
echo '</div>';

// Test Summary
echo '<div class="test-section"><h2>📊 Test Summary</h2>';

echo '<table>';
echo '<tr><th>Test</th><th>Result</th><th>Status</th></tr>';
foreach ($test_results as $test => $result) {
    $status_class = $result === 'PASS' ? 'success' : ($result === 'PARTIAL' ? 'warning' : 'error');
    $status_icon = $result === 'PASS' ? '✅' : ($result === 'PARTIAL' ? '⚠️' : '❌');
    echo '<tr>';
    echo '<td>' . ucwords(str_replace('_', ' ', $test)) . '</td>';
    echo '<td><span class="' . $status_class . '">' . $result . '</span></td>';
    echo '<td>' . $status_icon . '</td>';
    echo '</tr>';
}
echo '</table>';

$success_rate = ($passed_tests / $total_tests) * 100;

if ($success_rate >= 80) {
    echo '<div class="success"><h3>🎉 Tests Passed: ' . $passed_tests . '/' . $total_tests . ' (' . round($success_rate) . '%)</h3>';
    echo '<p>The modern transaction interface is ready for use!</p></div>';
} elseif ($success_rate >= 60) {
    echo '<div class="warning"><h3>⚠️ Tests Passed: ' . $passed_tests . '/' . $total_tests . ' (' . round($success_rate) . '%)</h3>';
    echo '<p>The interface has some issues but should be mostly functional.</p></div>';
} else {
    echo '<div class="error"><h3>❌ Tests Passed: ' . $passed_tests . '/' . $total_tests . ' (' . round($success_rate) . '%)</h3>';
    echo '<p>The interface has significant issues that need to be addressed.</p></div>';
}

echo '<div class="info"><h3>📋 Next Steps:</h3>';
echo '<ol>';
echo '<li><a href="' . admin_url('admin.php?page=farmfaucet&tab=transactions') . '" class="button">Test Transaction Interface</a></li>';
echo '<li>Check that users appear in the left sidebar</li>';
echo '<li>Click on users to see their details and transaction history</li>';
echo '<li>Test the search functionality</li>';
echo '<li>Test the transaction filters (All, Success, Failed)</li>';
echo '</ol></div>';

echo '</div>';

echo '</body></html>';
?>
