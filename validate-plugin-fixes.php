<?php

/**
 * Farm Faucet Plugin Validation Script
 * 
 * This script validates that all fixes have been applied correctly
 * and tests the core functionality.
 */

// Load WordPress
$wp_load_path = dirname(__FILE__) . '/../../../wp-load.php';
if (file_exists($wp_load_path)) {
    require_once($wp_load_path);
} else {
    die('WordPress not found. Please run this script from the plugin directory.');
}

// Security check
if (!function_exists('current_user_can') || !current_user_can('manage_options')) {
    die('You do not have permission to run this script.');
}

echo '<!DOCTYPE html>
<html>
<head>
    <title>Farm Faucet Plugin Validation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { background: #d4edda; color: #155724; padding: 10px; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 10px; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 10px; border: 1px solid #ffeaa7; border-radius: 4px; margin: 10px 0; }
        h1, h2 { color: #333; }
        .step { margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .status-ok { color: #155724; font-weight: bold; }
        .status-error { color: #721c24; font-weight: bold; }
        .status-warning { color: #856404; font-weight: bold; }
    </style>
</head>
<body>';

echo '<h1>🔍 Farm Faucet Plugin Validation</h1>';

global $wpdb;
$validation_results = [];
$critical_issues = [];
$warnings = [];

// Step 1: Database Structure Validation
echo '<div class="step"><h2>Step 1: Database Structure Validation</h2>';

$tables_to_check = [
    'farmfaucet_faucets' => [
        'required_columns' => [
            'id',
            'name',
            'faucet_type',
            'currency',
            'amount',
            'cooldown',
            'shortcode',
            'api_key',
            'captcha_type',
            'faucet_color',
            'is_enabled',
            'transparent_bg',
            'bg_style',
            'bg_color',
            'bg_gradient_start',
            'bg_gradient_end',
            'text_color',
            'text_shadow',
            'button_color',
            'border_color',
            'border_radius',
            'button_border_radius',
            'input_label_color',
            'input_placeholder_color',
            'form_bg_color',
            'form_transparent',
            'currency_id',
            'min_withdrawal',
            'available_currencies',
            'conversion_currencies',
            'view_style',
            'ads_only_conversion',
            'created_at',
            'updated_at'
        ]
    ],
    'farmfaucet_currencies' => [
        'required_columns' => [
            'id',
            'name',
            'code',
            'symbol',
            'base_currency',
            'exchange_rate',
            'color',
            'icon',
            'currency_type',
            'is_active',
            'created_by',
            'created_at',
            'updated_at'
        ]
    ],
    'farmfaucet_user_currency_balances' => [
        'required_columns' => [
            'id',
            'user_id',
            'currency_id',
            'balance',
            'last_updated'
        ]
    ],
    'farmfaucet_tg_bots' => [
        'required_columns' => [
            'id',
            'bot_name',
            'bot_token',
            'bot_username',
            'bot_type',
            'webhook_url',
            'created_at',
            'updated_at',
            'settings',
            'is_active'
        ]
    ]
];

echo '<table>';
echo '<tr><th>Table</th><th>Status</th><th>Missing Columns</th></tr>';

foreach ($tables_to_check as $table_name => $config) {
    $full_table_name = $wpdb->prefix . $table_name;
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$full_table_name}'") === $full_table_name;

    if (!$table_exists) {
        echo '<tr><td>' . $table_name . '</td><td class="status-error">❌ Missing</td><td>Table does not exist</td></tr>';
        $critical_issues[] = "Table {$table_name} does not exist";
        continue;
    }

    // Check columns
    $columns = $wpdb->get_results("SHOW COLUMNS FROM {$full_table_name}");
    $existing_columns = array_map(function ($col) {
        return $col->Field;
    }, $columns);

    $missing_columns = array_diff($config['required_columns'], $existing_columns);

    if (empty($missing_columns)) {
        echo '<tr><td>' . $table_name . '</td><td class="status-ok">✅ OK</td><td>All columns present</td></tr>';
        $validation_results[$table_name] = 'OK';
    } else {
        echo '<tr><td>' . $table_name . '</td><td class="status-warning">⚠️ Incomplete</td><td>' . implode(', ', $missing_columns) . '</td></tr>';
        $warnings[] = "Table {$table_name} missing columns: " . implode(', ', $missing_columns);
        $validation_results[$table_name] = 'Incomplete';
    }
}

echo '</table>';
echo '</div>';

// Step 2: Plugin Class Loading Validation
echo '<div class="step"><h2>Step 2: Plugin Class Loading Validation</h2>';

$required_classes = [
    'Farmfaucet_Admin',
    'Farmfaucet_Logger',
    'Farmfaucet_Security',
    'Farmfaucet_Settings_Manager',
    'Farmfaucet_Currency_Maker',
    'Farmfaucet_DB_Updater',
    'Farmfaucet_Installer'
];

echo '<table>';
echo '<tr><th>Class</th><th>Status</th><th>Notes</th></tr>';

foreach ($required_classes as $class_name) {
    if (class_exists($class_name)) {
        echo '<tr><td>' . $class_name . '</td><td class="status-ok">✅ Loaded</td><td>Class is available</td></tr>';
    } else {
        echo '<tr><td>' . $class_name . '</td><td class="status-error">❌ Missing</td><td>Class not loaded</td></tr>';
        $critical_issues[] = "Class {$class_name} not loaded";
    }
}

echo '</table>';
echo '</div>';

// Step 3: Settings Validation
echo '<div class="step"><h2>Step 3: Settings Validation</h2>';

$test_settings = [
    'farmfaucet_captcha_type' => 'hcaptcha',
    'farmfaucet_hcaptcha_sitekey' => '',
    'farmfaucet_hcaptcha_secret' => '',
    'farmfaucet_faucetpay_api' => '',
    'farmfaucet_currency' => 'LTC',
    'farmfaucet_amount' => '0.001',
    'farmfaucet_cooldown' => '3600'
];

echo '<table>';
echo '<tr><th>Setting</th><th>Status</th><th>Value</th></tr>';

foreach ($test_settings as $setting_name => $default_value) {
    $value = get_option($setting_name, 'NOT_SET');

    if ($value === 'NOT_SET') {
        echo '<tr><td>' . $setting_name . '</td><td class="status-warning">⚠️ Not Set</td><td>Using default</td></tr>';
        $warnings[] = "Setting {$setting_name} not configured";
    } else {
        echo '<tr><td>' . $setting_name . '</td><td class="status-ok">✅ Set</td><td>' . esc_html(substr($value, 0, 50)) . '</td></tr>';
    }
}

echo '</table>';
echo '</div>';

// Step 4: Faucet Creation Test
echo '<div class="step"><h2>Step 4: Faucet Creation Test</h2>';

// Try to create a test faucet with all required fields
$test_faucet_data = [
    'name' => 'Validation Test Faucet',
    'faucet_type' => 'stage',
    'currency' => 'LTC',
    'amount' => '0.001',
    'cooldown' => 3600,
    'shortcode' => 'test_validation_' . time(),
    'api_key' => '',
    'captcha_type' => 'hcaptcha',
    'faucet_color' => 'green',
    'transparent_bg' => 0,
    'bg_style' => 'solid',
    'bg_color' => '#f8fff8',
    'bg_gradient_start' => '#f8fff8',
    'bg_gradient_end' => '#e8f5e9',
    'text_color' => '#4CAF50',
    'text_shadow' => 'none',
    'button_color' => '#4CAF50',
    'border_color' => '#4CAF50',
    'border_radius' => '8px',
    'button_border_radius' => '25px',
    'input_label_color' => '#333333',
    'input_placeholder_color' => '#999999',
    'form_bg_color' => '#ffffff',
    'form_transparent' => 0,
    'is_enabled' => 1
];

if (class_exists('Farmfaucet_Logger')) {
    try {
        $faucet_id = Farmfaucet_Logger::create_faucet($test_faucet_data);

        if ($faucet_id) {
            echo '<div class="success">✅ Test faucet created successfully (ID: ' . $faucet_id . ')</div>';

            // Test faucet retrieval
            $retrieved_faucet = Farmfaucet_Logger::get_faucet($faucet_id);
            if ($retrieved_faucet) {
                echo '<div class="success">✅ Test faucet retrieved successfully</div>';

                // Test faucet update
                $update_data = ['name' => 'Updated Test Faucet'];
                $update_result = Farmfaucet_Logger::update_faucet($faucet_id, $update_data);
                if ($update_result) {
                    echo '<div class="success">✅ Test faucet updated successfully</div>';
                } else {
                    echo '<div class="warning">⚠️ Test faucet update failed</div>';
                    $warnings[] = 'Faucet update failed';
                }
            } else {
                echo '<div class="warning">⚠️ Test faucet retrieval failed</div>';
                $warnings[] = 'Faucet retrieval failed';
            }

            // Clean up - delete the test faucet
            $delete_result = Farmfaucet_Logger::delete_faucet($faucet_id);
            if ($delete_result) {
                echo '<div class="info">ℹ️ Test faucet cleaned up successfully</div>';
            } else {
                echo '<div class="warning">⚠️ Test faucet cleanup failed</div>';
            }

            $validation_results['faucet_creation'] = 'OK';
        } else {
            echo '<div class="error">❌ Failed to create test faucet</div>';
            $critical_issues[] = 'Faucet creation failed';
            $validation_results['faucet_creation'] = 'Failed';
        }
    } catch (Exception $e) {
        echo '<div class="error">❌ Exception during faucet creation: ' . esc_html($e->getMessage()) . '</div>';
        $critical_issues[] = 'Faucet creation exception: ' . $e->getMessage();
        $validation_results['faucet_creation'] = 'Exception';
    }
} else {
    echo '<div class="error">❌ Farmfaucet_Logger class not available</div>';
    $critical_issues[] = 'Farmfaucet_Logger class missing';
    $validation_results['faucet_creation'] = 'Class Missing';
}

echo '</div>';

// Step 5: Currency Creation Test
echo '<div class="step"><h2>Step 5: Currency Creation Test</h2>';

if (class_exists('Farmfaucet_Currency_Maker')) {
    try {
        // Test currency creation
        $test_currency_data = [
            'name' => 'Test Currency',
            'code' => 'TEST' . time(),
            'symbol' => 'TST',
            'base_currency' => 'LTC',
            'exchange_rate' => 1000.00000000,
            'color' => '#4CAF50',
            'currency_type' => 'earnings',
            'is_active' => 1,
            'created_by' => 1
        ];

        // This would need to be implemented in the Currency_Maker class
        echo '<div class="info">ℹ️ Currency creation test requires Currency_Maker implementation</div>';
        $validation_results['currency_creation'] = 'Not Implemented';
    } catch (Exception $e) {
        echo '<div class="error">❌ Exception during currency creation test: ' . esc_html($e->getMessage()) . '</div>';
        $critical_issues[] = 'Currency creation exception: ' . $e->getMessage();
        $validation_results['currency_creation'] = 'Exception';
    }
} else {
    echo '<div class="error">❌ Farmfaucet_Currency_Maker class not available</div>';
    $critical_issues[] = 'Farmfaucet_Currency_Maker class missing';
    $validation_results['currency_creation'] = 'Class Missing';
}

echo '</div>';

// Step 6: Summary
echo '<div class="step"><h2>🎯 Validation Summary</h2>';

if (empty($critical_issues) && empty($warnings)) {
    echo '<div class="success"><h2>🎉 All Validations Passed!</h2>';
    echo '<p>The Farm Faucet plugin appears to be working correctly.</p>';
    echo '<p><strong>You can now:</strong></p>';
    echo '<ul>';
    echo '<li>Create and manage faucets</li>';
    echo '<li>Configure settings</li>';
    echo '<li>Create custom currencies</li>';
    echo '<li>Use all plugin features</li>';
    echo '</ul>';
} else {
    if (!empty($critical_issues)) {
        echo '<div class="error"><h3>❌ Critical Issues Found:</h3><ul>';
        foreach ($critical_issues as $issue) {
            echo '<li>' . esc_html($issue) . '</li>';
        }
        echo '</ul></div>';
    }

    if (!empty($warnings)) {
        echo '<div class="warning"><h3>⚠️ Warnings:</h3><ul>';
        foreach ($warnings as $warning) {
            echo '<li>' . esc_html($warning) . '</li>';
        }
        echo '</ul></div>';
    }

    echo '<div class="info"><h3>📋 Recommended Actions:</h3>';
    echo '<ol>';
    if (!empty($critical_issues)) {
        echo '<li>Run the database schema fix script again</li>';
        echo '<li>Check that all required files are present</li>';
        echo '<li>Verify WordPress and plugin permissions</li>';
    }
    if (!empty($warnings)) {
        echo '<li>Configure missing settings in the admin panel</li>';
        echo '<li>Add missing database columns manually if needed</li>';
    }
    echo '</ol></div>';
}

echo '<p><a href="' . admin_url('admin.php?page=farmfaucet') . '" class="button button-primary">Go to Farm Faucet Admin</a></p>';
echo '</div>';

echo '</body></html>';
